---
type: "always_apply"
---

<Basic_Rules>
Environments:
- We are working with Python (miniconda).
- Always check if the Virtual Environment Prompt Indicator in the terminal is “sweep”. If not, run “conda activate sweep” to enable the virtual environment. 
- Python interpreter version: 3.12.11
- The "Type Checking Mode" option of Pyright has been set to "strict". Please comply with the relevant type checking specifications as strictly as possible. If you encounter problems that you can't solve, please stop responding and let me handle them manually.
- Some installed packages: nidaqmx, scipy, matplotlib, numpy
- **特别注意**，如需安装新的包，请在确保**终端运行于sweep环境**的前提下（环境标识符为“sweep”），使用conda install进行安装（如果终端询问是否确认安装，请输入并发送"y"）。如果conda安装失败或不提供此包，请停止响应并让我手动处理。

---

Main Task:
- 我们正在开发一个"sweeper400" package，它的主要功能是：协同控制NI数据采集卡（使用现有的"nidaqmx" package）和步进电机（使用"MT_API.dll"文件），自动化分步采集空间中不同位置的信号，并对信号进行处理，获取信号的空间分布等信息。
- "sweeper400" package 包含以下子包："measure"（包含NI数据采集相关module），"move"（包含步进电机控制相关module），"analyze"（包含信号和数据处理相关module），"sweeper"（协同调用其他子包，将功能封装为适用于特定任务的专用对象，供外部以简洁的方式使用）
- 请在开发中使用"logging"统一进行日志管理，合理为我们的全部代码配置日志输出（仅输出到Terminal即可，无需保存到本地），方便你监测代码的运行情况。
- 请在开发中遵循以下方式："sweeper400" package的所有文件（包括源代码和安装时的配置文件"pyproject.toml"）位于根目录的"sweeper400"。在"sweeper400\sweeper400"目录中编写源代码（实现所有的函数/类/方法/属性），在根目录的"tests"目录中编写测试代码调用"sweeper400" package（尚未安装，请直接通过路径调用）。
</Basic_Rules>